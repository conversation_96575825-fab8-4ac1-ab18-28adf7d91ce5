import React, {useEffect, useState} from 'react';
import {View, Text, FlatList, StyleSheet, TouchableOpacity} from 'react-native';
import {TypoSkin} from '../../../assets/skin/typography';
import {ColorThemes} from '../../../assets/skin/colors';
import HotNewsCard from '../card/HotNewsCard';
import {NewsItem} from '../../../redux/models/news';
import {newsAction} from '../../../redux/actions/newsAction';
import {useNavigation} from '@react-navigation/native';
import {RootScreen} from '../../../router/router';

const mockHotNewsData: NewsItem[] = [
  {
    Id: '1',
    Name: 'Vinfast ra mắt mẫu xe điện mới, giá cạnh tranh',
    Description: 'Vinfast ra mắt mẫu xe điện mới, giá cạnh tranh',
    Img: 'https://picsum.photos/seed/picsum/160/100',
    Content: 'Vinfast ra mắt mẫu xe điện mới, gi<PERSON> cạnh tranh',
    DateCreated: Date.now() - 3600 * 1000 * 2, // 2 giờ trước
    Likes: 100,
    Comments: 100,
    Views: 100,
    relativeUser: {image: '', title: ''},
  },
  {
    Id: '2',
    Name: 'Hội thảo "AI và Tương lai việc làm" sắp diễn ra',
    Description: 'Hội thảo "AI và Tương lai việc làm" sắp diễn ra',
    Img: 'https://picsum.photos/seed/random/160/100',
    Content: 'Hội thảo "AI và Tương lai việc làm" sắp diễn ra',
    DateCreated: Date.now() + 86400 * 1000 * 3, // Trong 3 ngày tới
    Likes: 100,
    Comments: 100,
    Views: 100,
    relativeUser: {image: '', title: ''},
  },
  {
    Id: '3',
    Name: 'Cập nhật thị trường chứng khoán: VN-Index tăng nhẹ',
    Description: 'Cập nhật thị trường chứng khoán: VN-Index tăng nhẹ',
    DateCreated: Date.now() - 60 * 1000 * 15, // 15 phút trước
    Img: 'https://picsum.photos/seed/news/160/100',
    Content: 'Cập nhật thị trường chứng khoán: VN-Index tăng nhẹ',
    Likes: 100,
    Comments: 100,
    Views: 100,
    relativeUser: {image: '', title: ''},
  },
  {
    Id: '4',
    Name: 'Giá vàng hôm nay: Tiếp tục đà giảm',
    Description: 'Giá vàng hôm nay: Tiếp tục đà giảm',
    DateCreated: Date.now() - 86400 * 1000, // 1 ngày trước
    Img: 'https://picsum.photos/seed/money/160/100',
    Content: 'Giá vàng hôm nay: Tiếp tục đà giảm',
    Likes: 100,
    Comments: 100,
    Views: 100,
    relativeUser: {image: '', title: ''},
  },
  {
    Id: '5',
    Name: 'Lễ hội âm nhạc Color Me Run trở lại Hà Nội',
    Description: 'Lễ hội âm nhạc Color Me Run trở lại Hà Nội',
    DateCreated: Date.now() + 86400 * 1000 * 10, // Trong 10 ngày tới
    Img: 'https://picsum.photos/seed/event/160/100',
    Content: 'Lễ hội âm nhạc Color Me Run trở lại Hà Nội',
    Likes: 100,
    Comments: 100,
    Views: 100,
    relativeUser: {image: '', title: ''},
  },
];

// ====== COMPONENT CHÍNH (MAIN COMPONENT) ======
interface HotNewsSectionProps {
  title?: string;
  onSeeMore?: () => void;
  isRefresh?: boolean;
}

const HotNewsSection: React.FC<HotNewsSectionProps> = ({
  title = 'Tin tức hot',
  isRefresh = false,
}) => {
  const navigation = useNavigation<any>();
  const [hotNews, setHotNews] = useState<NewsItem[]>([]);

  useEffect(() => {
    initData();
  }, [isRefresh]);

  const initData = async () => {
    const response = await newsAction.fetch({
      page: 1,
      size: 5,
      sortby: [{prop: 'Views', direction: 'DESC'}],
    });
    if (response.data.length > 0) setHotNews(response.data);
  };

  const onSeeMore = () => {
    navigation.navigate(RootScreen.NewsScreen);
  };

  // Không hiển thị gì nếu không có dữ liệu
  if (!hotNews || hotNews.length === 0) {
    return null;
  }

  return (
    <View style={styles.sectionContainer}>
      {/* Header */}
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>{title}</Text>
        {
          <TouchableOpacity onPress={onSeeMore}>
            <Text style={styles.seeMoreButton}>Xem thêm</Text>
          </TouchableOpacity>
        }
      </View>

      {/* News List */}
      <FlatList
        data={hotNews}
        renderItem={({item}) => <HotNewsCard item={item} />}
        keyExtractor={item => item.Id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.listContentContainer}
      />
    </View>
  );
};

export default HotNewsSection;

// ====== STYLES ======
const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f0f2f5',
  },
  sectionContainer: {
    marginTop: 20,
    backgroundColor: '#ffffff',
    paddingVertical: 15,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    marginBottom: 15,
  },
  sectionTitle: {
    ...TypoSkin.title2,
    fontWeight: 'bold',
    color: ColorThemes.light.infor_text_color,
  },
  seeMoreButton: {
    fontSize: 16,
    color: '#007bff',
  },
  listContentContainer: {
    paddingLeft: 15,
  },
  cardContainer: {
    width: 160,
    marginRight: 12,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    overflow: 'hidden',
  },
  cardImage: {
    width: '100%',
    height: 100,
  },
  cardTextContainer: {
    padding: 10,
  },
  cardTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#1c1e21',
    marginBottom: 4,
    height: 44,
  },
  cardType: {
    fontSize: 13,
    fontWeight: '500',
    marginBottom: 6,
    alignSelf: 'flex-start', // Làm cho background vừa với text
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    overflow: 'hidden',
  },

  cardTimestamp: {
    fontSize: 12,
    color: '#8a8d91',
  },
});

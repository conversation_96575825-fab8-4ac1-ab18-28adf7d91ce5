import React from 'react';
import {View, Text, Image, TouchableOpacity, StyleSheet} from 'react-native';
import {AppSvg} from 'wini-mobile-components';
import iconSvg from '../../../svg/icon';
import {ColorThemes} from '../../../assets/skin/colors';
import {formatTimestampToDate} from '../../../utils/Utils';
import {NewsItem} from '../../../redux/models/news';

// Props for the LatestNewsCard component
interface LatestNewsCardProps {
  item: NewsItem;
}

// Component thống kê tương tác (Likes, Comments, Views)
interface EngagementStatProps {
  svgIcon: string;
  count: number;
}

const EngagementStat: React.FC<EngagementStatProps> = ({svgIcon, count}) => (
  <View style={styles.statItem}>
    <AppSvg SvgSrc={svgIcon} size={20} />
    <Text style={styles.statCount}>{count}</Text>
  </View>
);

const LatestNewsCard: React.FC<LatestNewsCardProps> = ({item}) => {
  return (
    <TouchableOpacity style={styles.card}>
      <Image source={{uri: item.Img}} style={styles.thumbnail} />
      <View style={styles.contentArea}>
        <Text style={styles.title} numberOfLines={1}>
          {item.Name}
        </Text>
        <View style={styles.metadata}>
          <Text style={styles.category}>Tin tức</Text>
          <Text style={styles.date}>
            {formatTimestampToDate(item.DateCreated)}
          </Text>
        </View>
        <Text style={styles.description} numberOfLines={2}>
          {item.Description}
        </Text>
        <View style={styles.statsContainer}>
          <EngagementStat svgIcon={iconSvg.thumbUp} count={item.Likes} />
          <EngagementStat svgIcon={iconSvg.comment} count={item.Comments} />
          <EngagementStat
            svgIcon={iconSvg.eye}
            count={Number(item.Views || 0)}
          />
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  // Card Styles
  card: {
    backgroundColor: ColorThemes.light.white,
    borderRadius: 12,
    padding: 12,
    flexDirection: 'row',
    marginBottom: 16,
    // Shadow for iOS
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    // Shadow for Android
    elevation: 3,
  },
  thumbnail: {
    width: 100,
    height: '100%',
    borderRadius: 8,
    marginRight: 12,
  },
  contentArea: {
    flex: 1,
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 4,
  },
  metadata: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  category: {
    fontSize: 12,
    color: '#64748b',
    marginRight: 8,
  },
  date: {
    fontSize: 12,
    color: '#64748b',
  },
  description: {
    fontSize: 14,
    color: '#475569',
    lineHeight: 20,
    marginBottom: 8,
  },
  // Engagement Stats Styles
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  statIcon: {
    fontSize: 14,
    marginRight: 4,
  },
  statCount: {
    marginLeft: 4,
    fontSize: 14,
    color: '#64748b',
  },
});

export default LatestNewsCard;

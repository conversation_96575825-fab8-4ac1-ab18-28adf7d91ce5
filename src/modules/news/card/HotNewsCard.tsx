import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {NewsItem} from '../../../redux/models/news';
import {formatTimestampToDate} from '../../../utils/Utils';

interface NewsCardProps {
  item: NewsItem;
}

const NewsCard: React.FC<NewsCardProps> = ({item}) => {
  const displayType = 'Tin tức';
  const typeStyle = styles.cardTypeNews;

  return (
    <TouchableOpacity style={styles.cardContainer}>
      <Image source={{uri: item.Img}} style={styles.cardImage} />
      <View style={styles.cardTextContainer}>
        <Text style={styles.cardTitle} numberOfLines={2}>
          {item.Name}
        </Text>
        <Text style={[styles.cardType, typeStyle]}>{displayType}</Text>
        <Text style={styles.cardTimestamp}>
          {formatTimestampToDate(item.DateCreated)} {/* Sử dụng hàm format */}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    width: 160,
    marginRight: 12,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    overflow: 'hidden',
  },
  cardImage: {
    width: '100%',
    height: 100,
  },
  cardTextContainer: {
    padding: 10,
  },
  cardTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#1c1e21',
    marginBottom: 4,
    height: 44,
  },
  cardTypeEvent: {
    color: '#6d1b7b',
    backgroundColor: '#f3e5f5',
  },
  cardTypeNews: {
    color: '#006a1e',
    backgroundColor: '#e7f3ff',
  },
  cardType: {
    fontSize: 13,
    fontWeight: '500',
    marginBottom: 6,
    alignSelf: 'flex-start', // Làm cho background vừa với text
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    overflow: 'hidden',
  },
  cardTimestamp: {
    fontSize: 12,
    color: '#8a8d91',
  },
});

export default NewsCard;

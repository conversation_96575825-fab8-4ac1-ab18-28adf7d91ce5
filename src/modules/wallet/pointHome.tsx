import React, {ImageBackground, Text, View, Animated} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {AppSvg, FDialog} from 'wini-mobile-components';
import iconSvg from '../../svg/icon';
import {TypoSkin} from '../../assets/skin/typography';
import {ColorThemes} from '../../assets/skin/colors';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {useEffect, useState, useRef} from 'react';
import {DataController} from '../../base/baseController';
import {TransactionType, TransactionStatus} from '../../Config/Contanst';
import ConfigAPI from '../../Config/ConfigAPI';
import FastImage from 'react-native-fast-image';
import {TouchableOpacity} from 'react-native';
import {RootScreen} from '../../router/router';
import {useNavigation} from '@react-navigation/native';
import {Ultis} from '../../utils/Utils';
import {dialogCheckAcc} from '../../Screen/Layout/mainLayout';
import {getRankCustomer} from '../../redux/actions/customerAction';
import {useDispatch, useSelector} from 'react-redux';
import {RootState} from '../../redux/store/store';

// Skeleton Component
const SkeletonBox = ({
  width,
  height,
  style,
}: {
  width: number | string;
  height: number;
  style?: any;
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: false,
        }),
      ]),
    );
    animation.start();
    return () => animation.stop();
  }, []);

  const opacity = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  return (
    <Animated.View
      style={[
        {
          width,
          height,
          backgroundColor: '#ccc',
          borderRadius: 4,
          opacity,
        },
        style,
      ]}
    />
  );
};

const PointHome = () => {
  const dispatch = useDispatch<any>();
  const {rankInfo, rankInfoLoading} = useSelector(
    (state: RootState) => state.customer,
  );
  const customer = useSelectorCustomerState().data;
  const [totalReward, setTotalReward] = useState(0);
  const [currentRank, setCurrentRank] = useState<any>(null);
  const [isVip, setIsVip] = useState(false);
  const navigation = useNavigation<any>();
  const dialogRef = useRef<any>(null);
  useEffect(() => {
    if (customer?.Id) {
      dispatch(getRankCustomer({customer, TransactionStatus, TransactionType}));
    }
  }, [customer?.Id]);

  useEffect(() => {
    //focus effect
    const unsubscribe = navigation.addListener('focus', async () => {
      if (!customer?.Id) return;
      dispatch(getRankCustomer({customer, TransactionStatus, TransactionType}));
    });
    return unsubscribe;
  }, [navigation]);

  useEffect(() => {
    if (rankInfo?.achievedRank) {
      setIsVip(Number(rankInfo?.achievedRank?.Sort) > 1);
    }
  }, [rankInfo?.achievedRank]);

  const getColorVip = () => {
    if (isVip) {
      return ['#FFC043', '#FFD275', '#FFE0A3', '#FFF3DF'];
    }
    return ['#90C8FB', '#8DC4F7E5', '#B6F5FE'];
  };

  const getImageVip = () => {
    if (isVip) {
      return require('../../assets/bg_circle_orange.png');
    }
    return require('../../assets/bg04.png');
  };

  const getColorText = () => {
    if (isVip) {
      return ColorThemes.light.secondary6_darker_color;
    }
    return ColorThemes.light.primary_main_color;
  };

  return (
    <View
      style={{
        height: 82,
        backgroundColor: 'white',
        marginHorizontal: 16,
        marginTop: 16,
        position: 'relative',
        borderRadius: 10,
        overflow: 'hidden',
      }}>
      <FDialog ref={dialogRef} />
      {rankInfoLoading ? (
        <SkeletonBox width={'100%'} height={82} />
      ) : (
        <LinearGradient
          colors={getColorVip()}
          start={{x: 0, y: 0.5}}
          end={{x: 1, y: 0.5}}
          style={{
            borderRadius: 10,
            height: '100%',
            flex: 1,
            overflow: 'hidden',
            width: '100%',
          }}>
          <ImageBackground
            source={getImageVip()}
            resizeMode="contain"
            style={{
              position: 'absolute',
              bottom: -30,
              left: -20,
              width: 110,
              height: 110,
            }}
          />
          <View style={{paddingLeft: 76, paddingTop: 16}}>
            <View style={{flexDirection: 'row', alignItems: 'center', gap: 8}}>
              <AppSvg SvgSrc={iconSvg.moneyGold} size={20} />
              {rankInfoLoading ? (
                <SkeletonBox width={80} height={20} />
              ) : (
                <Text
                  style={{
                    ...TypoSkin.heading7,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  {Ultis.money(totalReward)} điểm
                </Text>
              )}
            </View>
            <View style={{flexDirection: 'row', alignItems: 'center', gap: 8}}>
              {rankInfoLoading ? (
                <>
                  <SkeletonBox
                    width={20}
                    height={20}
                    style={{borderRadius: 10}}
                  />
                  <SkeletonBox width={100} height={20} />
                </>
              ) : currentRank ? (
                <>
                  {currentRank.Icon ? (
                    <FastImage
                      source={{uri: ConfigAPI.urlImg + currentRank.Icon}}
                      style={{width: 20, height: 20}}
                      resizeMode="contain"
                    />
                  ) : (
                    <AppSvg SvgSrc={iconSvg.ruby} size={20} />
                  )}
                  <Text
                    style={{
                      ...TypoSkin.heading7,
                      color: ColorThemes.light.neutral_text_title_color,
                    }}>
                    Hạng {currentRank.Name}
                  </Text>
                </>
              ) : (
                <>
                  <AppSvg SvgSrc={iconSvg.ruby} size={20} />
                  <Text
                    style={{
                      ...TypoSkin.heading7,
                      color: ColorThemes.light.neutral_text_title_color,
                      fontWeight: '500',
                    }}>
                    Chưa có hạng
                  </Text>
                </>
              )}
            </View>
          </View>

          <TouchableOpacity
            style={{position: 'absolute', right: 16, top: 22}}
            onPress={() => {
              if (!customer?.Id) {
                dialogCheckAcc(dialogRef);
                return;
              }
              navigation.push(RootScreen.GiftExchange);
            }}>
            <LinearGradient
              start={{x: 0, y: 0}}
              end={{x: 1, y: 0}}
              colors={['#FFC043', '#FFD275', '#FFE0A3', '#FFF3DF']}
              style={{borderRadius: 8, minHeight: 32}}>
              <Text
                style={{
                  ...TypoSkin.body2,
                  color: getColorText(),
                  fontSize: 14,
                  fontWeight: '500',
                  paddingHorizontal: 8,
                  paddingVertical: 4,
                }}>
                Đổi quà
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        </LinearGradient>
      )}
    </View>
  );
};

export default PointHome;

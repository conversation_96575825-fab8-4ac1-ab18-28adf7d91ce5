/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useState} from 'react';
import {
  StyleSheet,
  View,
  Text,
  ActivityIndicator,
  FlatList,
  RefreshControl,
} from 'react-native';
import {useRoute} from '@react-navigation/native';
import {Title} from '../../Config/Contanst';
import SearchBar from '../../components/shop/Search';
import {DataController} from '../../base/baseController';
import {TypoSkin} from '../../assets/skin/typography';
import {InforHeader} from '../../Screen/Layout/headers/inforHeader';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {FLoading, ListTile, Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../assets/skin/colors';
import {Ultis} from '../../utils/Utils';
import {navigate, RootScreen} from '../../router/router';

const OrderCustomerDetail = () => {
  const route = useRoute<any>();
  const [dataSearch, setDataSearch] = useState<string>('');
  const [data, setData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const orderController = new DataController('Order');
  const CustomerInfo = useSelectorCustomerState().data;

  useEffect(() => {
    setIsLoading(true);
    getData(route?.params?.status);
  }, []);

  const getData = async (status: number) => {
    const response = await orderController.getPatternList({
      page: 1,
      size: 100,
      query: `@CustomerId: {${CustomerInfo?.Id}} @Status: [${status}]`,
      pattern: {
        CustomerId: ['Id', 'Name', 'Mobile', 'Email', 'AvatarUrl'],
        ShopId: ['Id', 'Name', 'Avatar'],
        AddressId: ['Id', 'Address'],
      },
    });
    if (response.code == 200) {
      // add shop item by response.Shop
      response.data = response.data.map((item: any) => {
        item.Shop = response.Shop.find((shop: any) => shop.Id == item.ShopId);
        return item;
      });
      setData(response.data);
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await getData(route?.params?.status);
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <FLoading visible={isLoading} />

      <InforHeader
        title={
          route?.params?.status == 1
            ? Title.New
            : route?.params?.status == 2
            ? Title.Processing
            : route?.params?.status == 3
            ? Title.Done
            : Title.Cancel
        }
      />
      <SearchBar setDataSearch={setDataSearch} />
      <View style={styles.orderInfo}>
        <Text style={styles.title}>Danh sách đơn hàng</Text>
        <Text style={styles.numberOrder}>
          {isLoading
            ? 'Đang tải dữ liệu...'
            : data?.length > 0
            ? data?.length + ' đơn hàng'
            : 'Chưa có đơn hàng nào'}
        </Text>
      </View>
      {isLoading ? (
        <ActivityIndicator size="large" color="#0000ff" />
      ) : (
        // show flatlist
        <FlatList
          data={data}
          style={{flex: 1, height: '100%'}}
          keyExtractor={(item, i) => `${i} ${item.Id}`}
          contentContainerStyle={{paddingHorizontal: 16, gap: 16}}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={['#0000ff']} // Android
              tintColor={'#0000ff'} // iOS
            />
          }
          renderItem={({item}) => {
            return (
              <View style={{flex: 1}}>
                <ListTile
                  onPress={() => {
                    navigate(RootScreen.OrderDetailPage, {
                      orderId: item.Id,
                    });
                  }}
                  style={{
                    padding: 16,
                    borderWidth: 1,
                    borderColor: ColorThemes.light.neutral_main_border_color,
                  }}
                  listtileStyle={{alignItems: 'flex-start'}}
                  title={
                    <View style={{flexDirection: 'row', alignItems: 'center'}}>
                      <Winicon
                        src="fill/shopping/store"
                        size={16}
                        color={ColorThemes.light.primary_main_color}
                      />
                      <Text style={{...TypoSkin.title3, marginLeft: 8}}>
                        {item?.Shop?.Name}
                      </Text>
                    </View>
                  }
                  subtitle={
                    <View style={{gap: 8, paddingTop: 8}}>
                      <Text style={{...TypoSkin.title4, color: '#999'}}>
                        Mã đơn hàng: {item?.Code}
                      </Text>
                      <Text style={{...TypoSkin.title4, color: '#999'}}>
                        {item?.DateCreated
                          ? Ultis.formatDateTime(item?.DateCreated, true)
                          : ''}
                      </Text>
                    </View>
                  }
                  trailing={
                    <View
                      style={{
                        paddingTop: 4,
                        flex: 1,
                        justifyContent: 'space-between',
                        alignItems: 'flex-end',
                      }}>
                      <Text
                        style={{
                          ...TypoSkin.body3,
                          color:
                            route?.params?.status == 1
                              ? ColorThemes.light.primary_main_color
                              : route?.params?.status == 2
                              ? ColorThemes.light.warning_main_color
                              : route?.params?.status == 3
                              ? ColorThemes.light.success_main_color
                              : ColorThemes.light.error_main_color,
                        }}>
                        {route?.params?.status == 1
                          ? Title.New
                          : route?.params?.status == 2
                          ? Title.Processing
                          : route?.params?.status == 3
                          ? Title.Done
                          : Title.Cancel}
                      </Text>
                    </View>
                  }
                  bottom={
                    <View
                      style={{
                        paddingTop: 16,
                        flex: 1,
                        width: '100%',
                        flexDirection: 'row',
                        justifyContent: 'flex-end',
                      }}>
                    <View sty></View>
                    </View>
                  }
                />
              </View>
            );
          }}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    maxHeight: 120,
  },
  orderInfo: {
    display: 'flex',
    marginLeft: 13,
  },
  title: {
    ...TypoSkin.title3,
    fontWeight: '500',
    fontFamily: 'roboto',
  },
  numberOrder: {
    ...TypoSkin.title4,
    marginTop: 10,
    color: '#999',
    marginBottom: 8,
  },
});

export default OrderCustomerDetail;
